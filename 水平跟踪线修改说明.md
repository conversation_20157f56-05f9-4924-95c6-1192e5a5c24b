# 水平跟踪线功能添加说明

## 修改概述
在原有的垂直跟踪线基础上，为压力-时间图、流速-时间图、容量-时间图分别添加了水平跟踪线功能。

## 详细修改内容

### 1. 新增成员变量声明（第27-34行）

**原代码：**
```csharp
//跟踪线
private LineAnnotation pressureCursorLine, flowCursorLine, volumeCursorLine,flowPressureCursorLine, volumeFlowCursorLine, pressureVolumeCursorLine;
//跟踪线文本
private TextAnnotation pressureTimeAnnotation, flowTimeAnnotation, volumeTimeAnnotation,flowPressureTimeAnnotation, volumeFlowTimeAnnotation, pressureVolumeTimeAnnotation;
```

**修改后：**
```csharp
//跟踪线
private LineAnnotation pressureCursorLine, flowCursorLine, volumeCursorLine,flowPressureCursorLine, volumeFlowCursorLine, pressureVolumeCursorLine;
// *** 新增：水平跟踪线 ***
private LineAnnotation pressureHorizontalCursorLine, flowHorizontalCursorLine, volumeHorizontalCursorLine;
//跟踪线文本
private TextAnnotation pressureTimeAnnotation, flowTimeAnnotation, volumeTimeAnnotation,flowPressureTimeAnnotation, volumeFlowTimeAnnotation, pressureVolumeTimeAnnotation;
// *** 新增：水平跟踪线的文本注释 ***
private TextAnnotation pressureHorizontalTimeAnnotation, flowHorizontalTimeAnnotation, volumeHorizontalTimeAnnotation;
```

### 2. 新增水平跟踪线初始化方法（第2360-2394行）

**新增方法：**
```csharp
// *** 新增：初始化水平跟踪线的方法 ***
/// <summary>
/// 为单个图表初始化水平跟踪线
/// </summary>
/// <param name="plotView">目标图表控件</param>
/// <param name="horizontalCursorLine">输出参数：创建的水平跟踪线</param>
/// <param name="horizontalTimeAnnotation">输出参数：创建的水平跟踪线文本注释</param>
/// <param name="textXPosition">文本注释的初始X轴位置</param>
private void InitializeHorizontalTracker(PlotView plotView,
    out LineAnnotation horizontalCursorLine,
    out TextAnnotation horizontalTimeAnnotation,
    double textXPosition)
{
    // 创建水平跟踪线
    horizontalCursorLine = new LineAnnotation
    {
        Type = LineAnnotationType.Horizontal,  // 水平线
        Y = 0,                                 // 初始Y位置
        Color = OxyColors.Blue,                // 蓝色
        StrokeThickness = 1,                   // 线宽
        LineStyle = LineStyle.None,            // 初始隐藏
    };

    // 创建文本注释
    horizontalTimeAnnotation = new TextAnnotation
    {
        TextPosition = new DataPoint(textXPosition, 0),  // 初始位置
        Text = "",                                       // 初始空文本
        StrokeThickness = 0,                            // 无边框
        TextHorizontalAlignment = OxyPlot.HorizontalAlignment.Left,  // 左对齐
    };

    // 将注释添加到图表中
    plotView.Model.Annotations.Add(horizontalCursorLine);
    plotView.Model.Annotations.Add(horizontalTimeAnnotation);
}
```

### 3. 修改初始化所有跟踪线方法（第2306-2326行）

**原代码：**
```csharp
private void InitializeAllTrackers()
{
    // 为各图表初始化跟踪线，初始文本位置设为0
    //时间图
    InitializeTracker(plotPressure, out pressureCursorLine, out pressureTimeAnnotation, 0);
    InitializeTracker(plotFlow, out flowCursorLine, out flowTimeAnnotation, 0);
    InitializeTracker(plotVolume, out volumeCursorLine, out volumeTimeAnnotation, 0);
    //环路图
    InitializeTracker(plotFlowPressure, out flowPressureCursorLine, out flowPressureTimeAnnotation, 0);
    InitializeTracker(plotVolumeFlow, out volumeFlowCursorLine, out volumeFlowTimeAnnotation, 0);
    InitializeTracker(plotPressureVolume, out pressureVolumeCursorLine, out pressureVolumeTimeAnnotation, 0);
}
```

**修改后：**
```csharp
private void InitializeAllTrackers()
{
    // 为各图表初始化跟踪线，初始文本位置设为0
    //时间图 - 垂直跟踪线
    InitializeTracker(plotPressure, out pressureCursorLine, out pressureTimeAnnotation, 0);
    InitializeTracker(plotFlow, out flowCursorLine, out flowTimeAnnotation, 0);
    InitializeTracker(plotVolume, out volumeCursorLine, out volumeTimeAnnotation, 0);
    //环路图
    InitializeTracker(plotFlowPressure, out flowPressureCursorLine, out flowPressureTimeAnnotation, 0);
    InitializeTracker(plotVolumeFlow, out volumeFlowCursorLine, out volumeFlowTimeAnnotation, 0);
    InitializeTracker(plotPressureVolume, out pressureVolumeCursorLine, out pressureVolumeTimeAnnotation, 0);

    // *** 新增：初始化水平跟踪线 ***
    //时间图 - 水平跟踪线
    InitializeHorizontalTracker(plotPressure, out pressureHorizontalCursorLine, out pressureHorizontalTimeAnnotation, 0);
    InitializeHorizontalTracker(plotFlow, out flowHorizontalCursorLine, out flowHorizontalTimeAnnotation, 0);
    InitializeHorizontalTracker(plotVolume, out volumeHorizontalCursorLine, out volumeHorizontalTimeAnnotation, 0);
}
```

### 4. 修改UpdateAllPlots方法（第2234-2255行）

**在原有的垂直跟踪线更新代码后添加：**
```csharp
// *** 新增：更新水平跟踪线 ***
// 更新压力图的水平跟踪线
pressureHorizontalCursorLine.Y = currentData?.气道压 ?? 0;
pressureHorizontalTimeAnnotation.Text = $" 压力: {currentData?.气道压 ?? 0:F2} cmH?O";
pressureHorizontalTimeAnnotation.TextPosition = new DataPoint(analyzer.MaxMinValues.Pressure.Max * 0.8, currentData?.气道压 ?? 0);

// 更新流速图的水平跟踪线
flowHorizontalCursorLine.Y = currentData?.流量 ?? 0;
flowHorizontalTimeAnnotation.Text = $" 流量: {currentData?.流量 ?? 0:F2} L/min";
flowHorizontalTimeAnnotation.TextPosition = new DataPoint(analyzer.MaxMinValues.Flow.Max * 0.8, currentData?.流量 ?? 0);

// 更新容量图的水平跟踪线
volumeHorizontalCursorLine.Y = currentData?.潮气量 ?? 0;
volumeHorizontalTimeAnnotation.Text = $" 容量: {currentData?.潮气量 ?? 0:F2} mL";
volumeHorizontalTimeAnnotation.TextPosition = new DataPoint(analyzer.MaxMinValues.Volume.Max * 0.8, currentData?.潮气量 ?? 0);
```

### 5. 修改SetAllCursorLinesVisible方法（第2277-2296行）

**在原有代码后添加：**
```csharp
// *** 新增：水平跟踪线的可见性控制 ***
pressureHorizontalCursorLine.LineStyle = style;
flowHorizontalCursorLine.LineStyle = style;
volumeHorizontalCursorLine.LineStyle = style;
```

### 6. 修改ClearAllAnnotations方法（第2298-2315行）

**在原有代码后添加：**
```csharp
// *** 新增：清空水平跟踪线的文本注释 ***
pressureHorizontalTimeAnnotation.Text = "";
flowHorizontalTimeAnnotation.Text = "";
volumeHorizontalTimeAnnotation.Text = "";
```

## 功能特点

1. **水平跟踪线颜色**：使用蓝色（OxyColors.Blue）以区别于红色的垂直跟踪线
2. **同步显示**：水平跟踪线与垂直跟踪线同时显示/隐藏
3. **实时更新**：水平跟踪线会根据当前时间点的数据值实时更新Y坐标位置
4. **文本注释**：每条水平跟踪线都有对应的数值显示
5. **位置优化**：文本注释位置设置在图表右侧80%位置，避免遮挡数据

## 使用方式

水平跟踪线的显示和隐藏与原有的垂直跟踪线完全同步：
- 右键点击图表可切换跟踪线的显示/隐藏状态
- 左键点击或键盘左右箭头键可移动跟踪线位置
- 水平跟踪线会自动跟随当前时间点的数据值进行Y轴定位

## 修改标记

所有新增的代码都使用了明显的注释标记：`// *** 新增：xxx ***` 以便于识别修改内容。
