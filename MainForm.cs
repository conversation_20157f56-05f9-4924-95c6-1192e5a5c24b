using System;
using System.Drawing;
using System.Windows.Forms;

namespace SimpleApp
{
    public partial class MainForm : Form
    {
        private TextBox nameTextBox;
        private TextBox num1TextBox;
        private TextBox num2TextBox;
        private Label resultLabel;
        private Button calculateButton;
        private Button greetButton;
        private Label timeLabel;
        private System.Windows.Forms.Timer timeTimer;

        public MainForm()
        {
            InitializeComponent();
            InitializeTimer();
        }

        private void InitializeComponent()
        {
            // 设置窗体属性
            this.Text = "简单的WinForms程序";
            this.Size = new Size(500, 400);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;

            // 创建控件
            CreateControls();
            LayoutControls();
            AttachEventHandlers();
        }

        private void CreateControls()
        {
            // 姓名输入
            var nameLabel = new Label
            {
                Text = "请输入您的姓名:",
                Location = new Point(20, 20),
                Size = new Size(120, 23),
                Font = new Font("Microsoft YaHei", 9F)
            };

            nameTextBox = new TextBox
            {
                Location = new Point(150, 18),
                Size = new Size(200, 23),
                Font = new Font("Microsoft YaHei", 9F)
            };

            greetButton = new Button
            {
                Text = "问候",
                Location = new Point(360, 17),
                Size = new Size(80, 25),
                Font = new Font("Microsoft YaHei", 9F),
                BackColor = Color.LightBlue
            };

            // 时间显示
            timeLabel = new Label
            {
                Text = $"当前时间: {DateTime.Now:yyyy年MM月dd日 HH:mm:ss}",
                Location = new Point(20, 60),
                Size = new Size(400, 23),
                Font = new Font("Microsoft YaHei", 9F),
                ForeColor = Color.Blue
            };

            // 计算器部分
            var calculatorLabel = new Label
            {
                Text = "简单计算器:",
                Location = new Point(20, 100),
                Size = new Size(100, 23),
                Font = new Font("Microsoft YaHei", 9F, FontStyle.Bold)
            };

            var num1Label = new Label
            {
                Text = "第一个数字:",
                Location = new Point(20, 130),
                Size = new Size(80, 23),
                Font = new Font("Microsoft YaHei", 9F)
            };

            num1TextBox = new TextBox
            {
                Location = new Point(110, 128),
                Size = new Size(100, 23),
                Font = new Font("Microsoft YaHei", 9F)
            };

            var num2Label = new Label
            {
                Text = "第二个数字:",
                Location = new Point(230, 130),
                Size = new Size(80, 23),
                Font = new Font("Microsoft YaHei", 9F)
            };

            num2TextBox = new TextBox
            {
                Location = new Point(320, 128),
                Size = new Size(100, 23),
                Font = new Font("Microsoft YaHei", 9F)
            };

            calculateButton = new Button
            {
                Text = "计算",
                Location = new Point(200, 170),
                Size = new Size(100, 30),
                Font = new Font("Microsoft YaHei", 9F),
                BackColor = Color.LightGreen
            };

            resultLabel = new Label
            {
                Text = "计算结果将显示在这里",
                Location = new Point(20, 220),
                Size = new Size(450, 120),
                Font = new Font("Microsoft YaHei", 9F),
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.LightYellow,
                TextAlign = ContentAlignment.TopLeft
            };

            // 添加控件到窗体
            this.Controls.AddRange(new Control[]
            {
                nameLabel, nameTextBox, greetButton,
                timeLabel,
                calculatorLabel,
                num1Label, num1TextBox,
                num2Label, num2TextBox,
                calculateButton,
                resultLabel
            });
        }

        private void LayoutControls()
        {
            // 控件布局已在CreateControls中设置
        }

        private void AttachEventHandlers()
        {
            greetButton.Click += GreetButton_Click;
            calculateButton.Click += CalculateButton_Click;
            nameTextBox.KeyPress += NameTextBox_KeyPress;
            num1TextBox.KeyPress += NumTextBox_KeyPress;
            num2TextBox.KeyPress += NumTextBox_KeyPress;
        }

        private void InitializeTimer()
        {
            timeTimer = new System.Windows.Forms.Timer();
            timeTimer.Interval = 1000; // 每秒更新一次
            timeTimer.Tick += TimeTimer_Tick;
            timeTimer.Start();
        }

        private void TimeTimer_Tick(object sender, EventArgs e)
        {
            timeLabel.Text = $"当前时间: {DateTime.Now:yyyy年MM月dd日 HH:mm:ss}";
        }

        private void GreetButton_Click(object sender, EventArgs e)
        {
            string name = nameTextBox.Text.Trim();
            string greeting;

            if (!string.IsNullOrEmpty(name))
            {
                greeting = $"你好, {name}!\n欢迎使用WinForms程序!";
            }
            else
            {
                greeting = "你好, 匿名用户!\n欢迎使用WinForms程序!";
            }

            MessageBox.Show(greeting, "问候", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void CalculateButton_Click(object sender, EventArgs e)
        {
            if (double.TryParse(num1TextBox.Text, out double num1) &&
                double.TryParse(num2TextBox.Text, out double num2))
            {
                string results = $"计算结果:\n\n";
                results += $"加法: {num1} + {num2} = {num1 + num2}\n";
                results += $"减法: {num1} - {num2} = {num1 - num2}\n";
                results += $"乘法: {num1} × {num2} = {num1 * num2}\n";

                if (num2 != 0)
                {
                    results += $"除法: {num1} ÷ {num2} = {num1 / num2:F2}";
                }
                else
                {
                    results += "除法: 不能除以零!";
                }

                resultLabel.Text = results;
            }
            else
            {
                MessageBox.Show("请输入有效的数字!", "输入错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void NameTextBox_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                GreetButton_Click(sender, e);
            }
        }

        private void NumTextBox_KeyPress(object sender, KeyPressEventArgs e)
        {
            // 只允许数字、小数点、负号和控制字符
            if (!char.IsControl(e.KeyChar) && !char.IsDigit(e.KeyChar) && 
                e.KeyChar != '.' && e.KeyChar != '-')
            {
                e.Handled = true;
            }

            // 只允许一个小数点
            if (e.KeyChar == '.' && (sender as TextBox).Text.IndexOf('.') > -1)
            {
                e.Handled = true;
            }

            // 负号只能在开头
            if (e.KeyChar == '-' && (sender as TextBox).SelectionStart != 0)
            {
                e.Handled = true;
            }

            // 回车键触发计算
            if (e.KeyChar == (char)Keys.Enter)
            {
                CalculateButton_Click(sender, e);
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                timeTimer?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
