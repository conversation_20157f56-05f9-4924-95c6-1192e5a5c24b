# 简单的WinForms程序

这是一个使用C# WinForms创建的简单桌面应用程序。

## 功能特性

### 1. 用户问候功能
- 输入姓名并点击"问候"按钮
- 程序会显示个性化的问候消息
- 支持回车键快捷操作

### 2. 实时时间显示
- 窗体上方显示当前时间
- 每秒自动更新时间显示
- 格式：yyyy年MM月dd日 HH:mm:ss

### 3. 简单计算器
- 支持两个数字的基本运算
- 包含加法、减法、乘法、除法
- 自动处理除零错误
- 支持小数和负数输入
- 支持回车键快捷计算

## 程序界面

程序界面包含以下元素：
- 姓名输入框和问候按钮
- 实时时间显示标签
- 两个数字输入框
- 计算按钮
- 结果显示区域

## 如何运行

### 方法1：使用dotnet命令
```bash
dotnet run
```

### 方法2：构建后运行
```bash
dotnet build
./bin/Debug/net8.0-windows/SimpleApp.exe
```

## 项目结构

- `SimpleApp.csproj` - 项目配置文件
- `Program.cs` - 程序入口点
- `MainForm.cs` - 主窗体类，包含所有UI逻辑
- `README.md` - 项目说明文档

## 技术特点

- 使用.NET 8.0 Windows Forms
- 采用事件驱动编程模式
- 实现了输入验证和错误处理
- 使用Timer控件实现实时更新
- 支持键盘快捷操作

## 用户体验优化

- 数字输入框只允许输入有效的数字字符
- 提供友好的错误提示消息
- 支持键盘操作提高效率
- 界面布局清晰，易于使用

## 扩展建议

可以考虑添加以下功能：
- 更多数学运算（平方根、幂运算等）
- 计算历史记录
- 主题切换功能
- 多语言支持
- 配置文件保存用户偏好
